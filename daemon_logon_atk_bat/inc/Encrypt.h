#ifndef _ENCRYPT_H_
#define _ENCRYPT_H_
#include <stdlib.h>
#include <cstdio>
#include <cstring>
#include <iostream>
using namespace std;

#include <openssl/aes.h>
#include <iomanip>
#include <sstream>
#include <openssl/sha.h>

// Include headers based on OpenSSL version
#ifdef ROCKY_LINUX_9
    // Rocky Linux 9 (OpenSSL 3.x)
    #include <openssl/modes.h>
    #define OPENSSL_SUPPRESS_DEPRECATED
#elif defined(CENTOS_7X)
    // CentOS 7.x (OpenSSL 1.0.2) - supports modes.h
    #include <openssl/modes.h>
#elif defined(CENTOS_6X)
    // CentOS 6.x (OpenSSL 1.0.1) - modes.h may not be available
    // In OpenSSL 1.0.1, modes.h may not exist or may be in a different location
#elif defined(OPENSSL_3X)
    #include <openssl/modes.h>
    #define OPENSSL_SUPPRESS_DEPRECATED
#elif defined(OPENSSL_11X)
    #include <openssl/modes.h>
#else
    // OpenSSL 1.0.x or earlier versions
    // Do not include modes.h header as it may not be available
#endif

#define KEY_SIZE 128
//#define BYTES_SIZE 4096
#define BYTES_SIZE 8192

struct ctr_state {
	unsigned char ivec[AES_BLOCK_SIZE];
	unsigned int num;
	unsigned char ecount[AES_BLOCK_SIZE];
};

class Encrypt 
{
public:
	void set_key();
	void set_key(const char *key);
	void set_key_from_config(const char *config_key);
	void init_ctr(struct ctr_state *state, const unsigned char *iv);
	void encrypt(unsigned char *indata, unsigned char *outdata, int bytes_read);
	void decrypt(unsigned char *indata, unsigned char *outdata, int bytes_read);
	string sha256(const string& input);	
	
	AES_KEY ase_key;
	unsigned char iv[16];
	unsigned char ckey[16];
};
#endif
